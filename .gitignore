# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
dist

# dependencies
node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Node.js dependencies
node_modules/
*.log
npm-debug.log
yarn-error.log

# Turborepo-specific
.turbo/
turbo.log

# Next.js build and cache files
.next/
out/
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# OS and editor files
.DS_Store
*.swp
*.swo
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

# Testing
coverage/
*.lcov

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# System Files
.env
build/

.note/

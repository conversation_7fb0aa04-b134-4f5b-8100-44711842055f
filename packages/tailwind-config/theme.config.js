export const palette = {
  white: '#FFFFFF',
  black: '#000000',
  primary: {
    100: '#fef6f1',
    150: '#fbe6d7',
    200: '#f9d6be',
    250: '#f7c6a4',
    300: '#f4b68a',
    350: '#f2a671',
    400: '#ef9657',
    450: '#ed873d',
    500: '#EB7623',
    550: '#de6815',
    600: '#c45c12',
    650: '#aa5010',
    700: '#90440d',
    750: '#77380b',
    800: '#5d2c09',
    850: '#432006',
    900: '#291304',
    DEFAULT: '#EB7623'
  },
  secondary: {
    100: '#f1f5fe',
    150: '#d7e3fb',
    200: '#bed1f9',
    250: '#a4bff7',
    300: '#8aaef4',
    350: '#719cf2',
    400: '#578aef',
    450: '#3d78ed',
    500: '#2466EB',
    550: '#1558e0',
    600: '#134fc8',
    650: '#1046b1',
    700: '#0e3d99',
    750: '#0c3382',
    800: '#0a2a6b',
    850: '#082153',
    900: '#06183c',
    DEFAULT: '#2466EB'
  },
  info: {
    100: '#f1f8fd',
    150: '#daecfa',
    200: '#c3e1f8',
    250: '#acd5f5',
    300: '#94c9f2',
    350: '#7dbeef',
    400: '#66b2ec',
    450: '#4fa6e9',
    500: '#379ae6',
    550: '#1d8de3',
    600: '#197dca',
    650: '#166db0',
    700: '#125d95',
    750: '#0f4c7b',
    800: '#0c3c61',
    850: '#092c47',
    900: '#061c2d',
    DEFAULT: '#379ae6'
  },
  warning: {
    100: '#fef9ee',
    150: '#fcf0d7',
    200: '#fae7c0',
    250: '#f8dea9',
    300: '#f6d491',
    350: '#f4cb7a',
    400: '#f2c263',
    450: '#f0b94b',
    500: '#efb034',
    550: '#eca517',
    600: '#d29211',
    650: '#b57e0f',
    700: '#98690c',
    750: '#7a550a',
    800: '#5d4108',
    850: '#402c05',
    900: '#221803',
    DEFAULT: '#efb034'
  },
  danger: {
    100: '#fdf2f2',
    150: '#f9dbdc',
    200: '#f5c4c6',
    250: '#f1adaf',
    300: '#ed9699',
    350: '#e97f83',
    400: '#e5696d',
    450: '#e25256',
    500: '#de3b40',
    550: '#d9252b',
    600: '#c12126',
    650: '#aa1d22',
    700: '#93191d',
    750: '#7b1518',
    800: '#641114',
    850: '#4d0d0f',
    900: '#36090b',
    DEFAULT: '#de3b40'
  },
  success: {
    100: '#eefdf3',
    150: '#d3f9e0',
    200: '#b8f5cd',
    250: '#9df2b9',
    300: '#82eea6',
    350: '#67ea93',
    400: '#4ce77f',
    450: '#31e36c',
    500: '#1dd75b',
    550: '#1ac052',
    600: '#17a948',
    650: '#14923e',
    700: '#117b34',
    750: '#0e642a',
    800: '#0a4d20',
    850: '#073517',
    900: '#041e0d',
    DEFAULT: '#1dd75b'
  },
  'color-1': {
    100: '#fef1f1',
    150: '#fce0e0',
    200: '#facfcf',
    250: '#f9bfbf',
    300: '#f7aeae',
    350: '#f59d9d',
    400: '#f48c8c',
    450: '#f27b7b',
    500: '#F06A6A',
    550: '#ed4a4a',
    600: '#ea2a2a',
    650: '#da1616',
    700: '#ba1212',
    750: '#9a0f0f',
    800: '#790c0c',
    850: '#590909',
    900: '#380606',
    DEFAULT: '#F06A6A'
  },
  'color-2': {
    100: '#f3fde5',
    150: '#e8facd',
    200: '#dcf8b5',
    250: '#d1f69d',
    300: '#c5f485',
    350: '#baf16c',
    400: '#afef54',
    450: '#a3ed3c',
    500: '#98EB24',
    550: '#89dc14',
    600: '#78c112',
    650: '#67a50f',
    700: '#568a0d',
    750: '#456f0a',
    800: '#345308',
    850: '#233805',
    900: '#121d03',
    DEFAULT: '#98EB24'
  },
  'color-3': {
    100: '#effcfd',
    150: '#d5f8fb',
    200: '#bcf4f9',
    250: '#a2eff6',
    300: '#89ebf4',
    350: '#70e7f2',
    400: '#56e3ef',
    450: '#3ddeed',
    500: '#24DAEB',
    550: '#14ccdc',
    600: '#12b3c1',
    650: '#0f9aa6',
    700: '#0d818b',
    750: '#0a6870',
    800: '#084f55',
    850: '#05363b',
    900: '#031d20',
    DEFAULT: '#24DAEB'
  }
};

export const fontSize = {
  t1: ['0.6875rem', { lineHeight: '1.125rem' }],
  t2: ['0.75rem', { lineHeight: '1.25rem' }],
  t3: ['0.875rem', { lineHeight: '1.375rem' }],
  t4: ['1rem', { lineHeight: '1.625rem' }],
  t5: ['1.125rem', { lineHeight: '1.75rem' }],
  t6: ['1.25rem', { lineHeight: '1.875rem' }],
  t7: ['1.5rem', { lineHeight: '2.25rem' }],
  t8: ['2rem', { lineHeight: '3rem' }],
  t9: ['2.5rem', { lineHeight: '3.5rem' }],
  t10: ['3rem', { lineHeight: '4.25rem' }],
  't10-1': ['4rem', { lineHeight: '5.25rem' }],
  't10-2': ['5rem', { lineHeight: '6.5rem' }],
  t11: ['6.25rem', { lineHeight: '8.125rem' }],
  t12: ['12.5rem', { lineHeight: '15rem' }],
  t13: ['18.75rem', { lineHeight: '22.5rem' }],
  t14: ['31.25rem', { lineHeight: '37.5rem' }]
};

const theme = {
  extend: {
    fontSize: { ...fontSize },
    spacing: {
      s0: '0.125rem',
      s1: '0.25rem',
      s2: '0.375rem',
      s3: '0.5rem',
      s4: '0.75rem',
      s5: '1rem',
      s6: '1.25rem',
      s7: '1.5rem',
      s8: '1.75rem',
      s9: '2rem',
      s10: '2.25rem',
      s11: '2.5rem',
      s12: '2.75rem',
      s13: '3rem',
      s14: '3.5rem',
      s15: '4rem',
      s16: '6rem',
      s17: '8rem',
      s18: '12rem',
      s19: '16rem',
      s20: '24rem'
    },
    fontFamily: {
      heading: 'Poppins',
      body: 'NunitoSans'
    },
    width: {
      Sz_NONE: '0rem',
      Sz0: '0.125rem',
      Sz1: '0.25rem',
      Sz2: '0.375rem',
      Sz3: '0.5rem',
      Sz4: '0.75rem',
      Sz5: '1rem',
      Sz6: '1.25rem',
      Sz7: '1.5rem',
      Sz8: '1.75rem',
      Sz9: '2rem',
      Sz10: '2.25rem',
      Sz11: '2.5rem',
      Sz12: '2.75rem',
      Sz13: '3rem',
      Sz14: '3.25rem',
      Sz15: '3.5rem',
      Sz16: '3.75rem',
      Sz17: '4rem',
      Sz18: '6rem',
      Sz19: '8rem',
      Sz20: '12rem',
      Sz21: '16rem',
      Sz22: '24rem',
      Sz23: '32rem',
      Sz24: '40rem',
      Sz25: '48rem',
      Sz26: '56rem',
      Sz27: '64rem'
    },
    height: {
      Sz_NONE: '0rem',
      Sz0: '0.125rem',
      Sz1: '0.25rem',
      Sz2: '0.375rem',
      Sz3: '0.5rem',
      Sz4: '0.75rem',
      Sz5: '1rem',
      Sz6: '1.25rem',
      Sz7: '1.5rem',
      Sz8: '1.75rem',
      Sz9: '2rem',
      Sz10: '2.25rem',
      Sz11: '2.5rem',
      Sz12: '2.75rem',
      Sz13: '3rem',
      Sz14: '3.25rem',
      Sz15: '3.5rem',
      Sz16: '3.75rem',
      Sz17: '4rem',
      Sz18: '6rem',
      Sz19: '8rem',
      Sz20: '12rem',
      Sz21: '16rem',
      Sz22: '24rem',
      Sz23: '32rem',
      Sz24: '40rem',
      Sz25: '48rem',
      Sz26: '56rem',
      Sz27: '64rem'
    },
    borderRadius: {
      xs: '0.1875rem',
      s: '0.25rem',
      m: '0.375rem',
      l: '0.5rem',
      xl: '0.75rem',
      '100-percent': '100%'
    },
    boxShadow: {
      xs: '0px 0px 1px rgba(23, 26, 31, 0.15), 0px 0px 2px rgba(23, 26, 31, 0.2)',
      s: '0px 2px 5px rgba(23, 26, 31, 0.17), 0px 0px 2px rgba(23, 26, 31, 0.2)',
      m: '0px 4px 9px rgba(23, 26, 31, 0.19), 0px 0px 2px rgba(23, 26, 31, 0.2)',
      l: '0px 8px 17px rgba(23, 26, 31, 0.23), 0px 0px 2px rgba(23, 26, 31, 0.2)',
      xl: '0px 17px 35px rgba(23, 26, 31, 0.32), 0px 0px 2px rgba(23, 26, 31, 0.2)'
    }
  }
};

export default theme;

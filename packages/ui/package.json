{"name": "@repo/ui", "version": "0.0.0", "type": "module", "private": true, "files": ["dist"], "exports": {".": "./src/index.ts", "./styles.css": "./dist/index.css"}, "scripts": {"build": "npx @tailwindcss/cli -i ./src/styles/globals.css -o ./dist/index.css", "lint": "eslint src/", "dev": "npx @tailwindcss/cli -i ./src/styles/globals.css -o ./dist/index.css --watch", "type-check": "tsc --noEmit", "generate:component": "turbo gen react-component", "add-ui": "pnpm dlx shadcn@latest add"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^1.12.4", "@types/node": "^20.11.24", "@types/react": "18.3.0", "@types/react-dom": "18.3.1", "autoprefixer": "^10.4.18", "postcss": "^8.5.3", "tailwindcss": "^4.0.13", "typescript": "5.5.4"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-slot": "^1.1.2", "@shadcn/ui": "^0.0.4", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.0.13", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.471.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.17"}}
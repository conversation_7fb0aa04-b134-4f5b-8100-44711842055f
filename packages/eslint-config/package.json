{"name": "@repo/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "^9.17.0", "@next/eslint-plugin-next": "^15.1.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-turbo": "^2.3.0", "globals": "^15.12.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.13", "typescript": "^5.3.3", "typescript-eslint": "^8.15.0", "eslint-plugin-tailwindcss": "^3.18.0"}}
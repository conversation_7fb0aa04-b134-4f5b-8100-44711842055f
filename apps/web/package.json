{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@repo/ui": "workspace:*", "@tailwindcss/postcss": "^4.0.13", "next": "^15.1.0", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-hook-form": "^7.62.0", "zod": "^4.0.17"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.17.24", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "autoprefixer": "^10.4.20", "clsx": "^2.1.1", "postcss": "^8.5.3", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.0.13", "typescript": "5.5.4"}}
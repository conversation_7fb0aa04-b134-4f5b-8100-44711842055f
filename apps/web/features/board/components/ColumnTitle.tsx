import { PlusCircleIcon } from '@heroicons/react/24/outline';

const ColumnTitle = (props: { children: React.ReactNode; onAdd?: () => void }) => {
  const { onAdd, children } = props;

  return (
    <div className="flex items-center justify-between">
      <p className="text-t4 font-body font-semibold text-neutral-900">{children}</p>
      <PlusCircleIcon onClick={onAdd} className="size-6 cursor-pointer text-neutral-500" />
    </div>
  );
};

export default ColumnTitle;

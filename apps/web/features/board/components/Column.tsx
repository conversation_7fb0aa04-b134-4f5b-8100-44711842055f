'use client';

import { cn } from '@/libs/utils';
import React from 'react';
import { DropTargetMonitor, useDrop } from 'react-dnd';
import { BoardState } from '../constant/task';
import { DragItem, Task } from '../types/task';
import TaskItem from './TaskItem';

type ColumnProps = {
  status: BoardState;
  tasks: Task[];
  isDragging: boolean;

  moveTask: ({
    id,
    sourceStatus,
    targetStatus
  }: {
    id: string;
    sourceStatus: BoardState;
    targetStatus: BoardState;
  }) => void;
  onDrag: (isDragging: boolean) => void;
};

const useColumn = ({ moveTask, status }: { status: BoardState; moveTask: ColumnProps['moveTask'] }) => {
  const [targetColumn, setTargetColumn] = React.useState<BoardState | null>(null);
  const [isDragging, setIsDragging] = React.useState(false);

  const [{ isOver }, drop] = useDrop({
    drop: (item: DragItem) => {
      setIsDragging(false);
      setTargetColumn(null);
      return moveTask({ id: item.id, sourceStatus: item.status, targetStatus: status });
    },
    hover: (item: DragItem) => {
      if (!targetColumn) {
        setTargetColumn(item.status);
        setIsDragging(true);
      }
    },
    accept: 'TASK',
    collect: (monitor: DropTargetMonitor) => ({
      isOver: !!monitor.isOver()
    })
  });

  React.useEffect(() => {
    if (!isOver) {
      setIsDragging(false);
      setTargetColumn(null);
    }
    return () => {
      setIsDragging(false);
      setTargetColumn(null);
    };
  }, [isOver]);

  return { isDragging, targetColumn, drop };
};

const Column = (props: ColumnProps) => {
  const { status, tasks, moveTask, onDrag } = props;

  const { isDragging, targetColumn, drop } = useColumn({ status, moveTask });

  return (
    <div
      className={cn(
        'flex h-full min-h-80 flex-col gap-3 rounded-lg bg-neutral-100 p-2',
        { 'border-primary border-2': targetColumn === status },
        { 'h-full bg-orange-200': isDragging }
      )}
      ref={drop as React.ForwardedRef<HTMLDivElement>}
    >
      {tasks?.map((task) => {
        return <TaskItem key={task.id} onDrag={onDrag} taskInfo={task} status={status} />;
      })}
    </div>
  );
};

export default Column;

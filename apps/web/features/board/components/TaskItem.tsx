'use client';

import { BoardState } from '@/features/board/constant/task';
import { Task } from '@/features/board/types/task';
import { ChatBubbleLeftIcon, PaperClipIcon } from '@heroicons/react/24/outline';
import { Avatar, AvatarFallback, AvatarImage, Card, CardContent, CardDescription } from '@repo/ui';
import Image from 'next/image';
import React from 'react';
import { useDrag } from 'react-dnd';
import { cn } from '../../../libs/utils';
import Flex from '../../../shared/components/ui/flex';

const TagItem = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="">
      <p className="text-primary text-t1 font-body bg-primary-100 rounded-xl border-transparent px-1.5 py-1">
        {children}
      </p>
    </div>
  );
};

type Props = {
  taskInfo: Task;
  status: BoardState;
  onDrag: (isDragging: boolean) => void;
};

const TaskItem = (props: Props) => {
  const { taskInfo, status, onDrag } = props;
  const { id, image, labels, description, attachments, comments } = taskInfo;

  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'TASK',
    item: { id, status },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging()
    })
  }));

  React.useEffect(() => {
    onDrag(isDragging);
  }, [isDragging]);

  return (
    <Card
      ref={drag}
      className={cn('shadow-xs flex flex-col gap-4 rounded-md border-0 border-none bg-white p-4', {
        'cursor-grab': !isDragging,
        'cursor-grabbing': isDragging
      })}
    >
      {image && <Image src={image} alt="Card image" className="rounded-md" width={200} height={100} />}

      <CardContent className="flex flex-col gap-4 p-0">
        <CardDescription className="font-body text-neutral-900">{description}</CardDescription>
        <Flex className="gap-2">
          {labels.map((label) => (
            <TagItem key={label}>{label}</TagItem>
          ))}
        </Flex>

        <Flex justifyContent="between">
          <Flex className="gap-4">
            <Flex className="gap-0.5">
              <PaperClipIcon className="size-4 text-neutral-500" />
              <div className="font-body text-t3 text-neutral-500">{attachments}</div>
            </Flex>

            <Flex className="gap-0.5">
              <ChatBubbleLeftIcon className="size-4 text-neutral-500" />
              <div className="font-body text-t3 text-neutral-500">{comments}</div>
            </Flex>
          </Flex>
          <Flex>
            <Avatar className="h-8 w-8 rounded-2xl">
              <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
              <AvatarFallback>CN</AvatarFallback>
            </Avatar>
          </Flex>
        </Flex>
      </CardContent>
    </Card>
  );
};

export default TaskItem;

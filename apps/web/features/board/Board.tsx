'use client';

import React from 'react';

import { cn } from '@/libs/utils';
import { PlusIcon } from '@heroicons/react/24/outline';
import { But<PERSON> } from '@repo/ui';
import ColumnTitle from '../board/components/ColumnTitle';
import Column from './components/Column';
import { BoardState } from './constant/task';
import { Task } from './types/task';

const boardMock = {
  not_ready: [
    {
      id: '1',
      title: 'Design homepage',
      description: 'Create wireframes and mockups for the homepage redesign',
      image: null,
      labels: ['UI/UX', 'Design', 'High Priority'],
      avatars: ['https://example.com/avatars/jane.jpg', 'https://example.com/avatars/mark.jpg'],
      attachments: 3, // Amount of attachments
      comments: 5 // Amount of comments
    }
  ],
  to_do: [
    {
      id: '2',
      title: 'Implement authentication API',
      description: 'Set up endpoints for user login and registration',
      image: null,
      labels: ['Backend', 'API', 'Security'],
      avatars: ['https://example.com/avatars/alice.jpg'],
      attachments: 1,
      comments: 2
    },
    {
      id: '3',
      title: 'Write unit tests',
      description: 'Add test cases for the payment module',
      image: null, // No image for this task
      labels: ['Testing', 'QA'],
      avatars: ['https://example.com/avatars/bob.jpg', 'https://example.com/avatars/eve.jpg'],
      attachments: 0,
      comments: 3
    }
  ],
  in_progress: [
    {
      id: '4',
      title: 'Refactor dashboard',
      description: 'Optimize code and improve loading speed',
      image: null,
      labels: ['Frontend', 'Performance'],
      avatars: ['https://example.com/avatars/john.jpg', 'https://example.com/avatars/sarah.jpg'],
      attachments: 2,
      comments: 7
    }
  ],
  completed: [
    {
      id: '5',
      title: 'Deploy v1.0 to production',
      description: 'Push the latest build to the live server',
      image: null,
      labels: ['DevOps', 'Release'],
      avatars: ['https://example.com/avatars/mike.jpg'],
      attachments: 4,
      comments: 1
    }
  ]
} satisfies Record<string, Task[]>;

const useBoard = () => {
  const [board, setBoard] = React.useState<Record<BoardState, Task[]> | null>(null);

  const moveTask = ({
    id,
    sourceStatus,
    targetStatus
  }: {
    id: string;
    sourceStatus: BoardState;
    targetStatus: BoardState;
  }) => {
    if (sourceStatus === targetStatus) return;

    setBoard((prev) => {
      if (!prev) return null;

      const sourceTasks = [...prev[sourceStatus]];
      const targetTasks = [...prev[targetStatus]];
      const taskIndex = sourceTasks.findIndex((task) => task.id === id);
      const [task] = sourceTasks.splice(taskIndex, 1);
      targetTasks.push(task as Task);

      return {
        ...prev,
        [sourceStatus]: sourceTasks,
        [targetStatus]: targetTasks
      };
    });
  };

  React.useEffect(() => {
    setBoard(boardMock);
  }, []);

  return { board, moveTask };
};

const AddTaskButton = () => {
  return (
    <Button variant="ghost">
      <div
        className={cn(
          'w-full rounded-md p-2 text-xs text-neutral-400 hover:bg-neutral-100',
          'flex-items flex w-full justify-center gap-2'
        )}
      >
        <PlusIcon />
        <p>Add task</p>
      </div>
    </Button>
  );
};

const Board = () => {
  const { board, moveTask } = useBoard();

  const [isDragging, setIsDragging] = React.useState(false);

  const handleDrag = (isDragging: boolean) => {
    setIsDragging(isDragging);
  };

  return (
    <div className="flex flex-col gap-6">
      <p className="text-t8 font-heading font-bold text-neutral-900">Board</p>

      <div className="flex w-full gap-6">
        <div className="flex min-w-[250px] basis-1/4 flex-col gap-3.5">
          <ColumnTitle>{'Not ready'}</ColumnTitle>

          <Column
            isDragging={isDragging}
            status={BoardState.NOT_READY}
            tasks={board?.not_ready || []}
            onDrag={handleDrag}
            moveTask={moveTask}
          />
          <AddTaskButton />
        </div>

        <div className="flex min-w-[250px] basis-1/4 flex-col gap-3.5">
          <ColumnTitle>{'To do'}</ColumnTitle>

          <Column
            isDragging={isDragging}
            status={BoardState.TO_DO}
            tasks={board?.to_do || []}
            onDrag={handleDrag}
            moveTask={moveTask}
          />
          <AddTaskButton />
        </div>

        <div className="flex min-w-[250px] basis-1/4 flex-col gap-3.5">
          <ColumnTitle>{'In progress'}</ColumnTitle>

          <Column
            isDragging={isDragging}
            status={BoardState.IN_PROGRESS}
            tasks={board?.in_progress || []}
            onDrag={handleDrag}
            moveTask={moveTask}
          />
          <AddTaskButton />
        </div>

        <div className="flex min-w-[250px] basis-1/4 flex-col gap-3.5">
          <ColumnTitle>{'Completed'}</ColumnTitle>

          <Column
            isDragging={isDragging}
            status={BoardState.COMPLETED}
            tasks={board?.completed || []}
            onDrag={handleDrag}
            moveTask={moveTask}
          />
          <AddTaskButton />
        </div>
      </div>
    </div>
  );
};

export default Board;

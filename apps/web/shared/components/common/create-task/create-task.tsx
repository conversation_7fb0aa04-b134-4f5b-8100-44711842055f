import { PlusIcon, XMarkIcon } from '@heroicons/react/24/outline';
import {
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@repo/ui';

const CreateTaskButton = () => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="flex items-center gap-2">
          <PlusIcon className="size-4" />
          Add task
        </Button>
      </DialogTrigger>

      <DialogContent className="flex w-full max-w-2xl flex-col bg-white">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogDescription>Add a task to the board</DialogDescription>
            <DialogClose>
              <XMarkIcon className="size-6" />
            </DialogClose>
          </div>

          <DialogTitle>Create task</DialogTitle>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};

export default CreateTaskButton;

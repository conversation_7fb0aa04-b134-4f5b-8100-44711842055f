'use client';

import { cn } from '@/libs/utils';
import React from 'react';

type Props = {
  children: React.ReactNode;
  direction?: 'row' | 'column';
  justifyContent?: 'start' | 'end' | 'center' | 'between' | 'around' | 'even';
  alignItems?: 'start' | 'end' | 'center' | 'stretch' | 'baseline';
} & React.HTMLAttributes<HTMLDivElement>;

const flexDirection = { row: 'flex-row', column: 'flex-col' };

const justifyContent = {
  start: 'justify-start',
  end: 'justify-end',
  center: 'justify-center',
  between: 'justify-between',
  around: 'justify-around',
  even: 'justify-evenly'
};

const alignItems = {
  start: 'items-start',
  end: 'items-end',
  center: 'items-center',
  stretch: 'items-stretch',
  baseline: 'items-baseline'
};

const Flex = (props: Props) => {
  const {
    children,
    direction = 'row',
    justifyContent: justify = 'start',
    alignItems: align = 'center',
    ...rest
  } = props;

  return (
    <div
      {...rest}
      className={cn('flex', flexDirection[direction], justifyContent[justify], alignItems[align], rest.className)}
    >
      {children}
    </div>
  );
};

export default Flex;

'use client';

import { NavigationMenu, NavigationMenuItem, NavigationMenuList } from '@repo/ui';
import React from 'react';

import { cn } from '@/libs/utils';
import { ROUTE } from '@/shared/constants/path';
import { DocumentTextIcon, ViewColumnsIcon } from '@heroicons/react/24/outline';
import { usePathname, useRouter } from 'next/navigation';
import Flex from '../../ui/flex';

type Menu = {
  title: string;
  href: string;
  icon: React.ReactNode;
};

const MenuItem = ({
  children,
  selected,
  onClick
}: {
  selected: boolean;
  children: React.ReactNode;
  onClick: () => void;
}) => {
  return (
    <NavigationMenuItem
      onClick={onClick}
      className={cn(
        'text-neutral-650 w-full cursor-pointer',
        selected ? 'text-primary font-bold' : 'text-neutral-650 hover:text-primary-400 font-normal'
      )}
    >
      {children}
    </NavigationMenuItem>
  );
};

const menu = [
  {
    title: 'Backlog',
    href: ROUTE.BACKLOG,
    icon: <DocumentTextIcon className="size-4" />
  },
  { title: 'Board', href: ROUTE.HOME, icon: <ViewColumnsIcon className="size-4" /> }
  // { title: 'Report', href: ROUTE.HOME, icon: <></> },
  // { title: 'Inbox', href: ROUTE.HOME, icon: <></> },
  // { title: 'Settings', href: ROUTE.HOME, icon: <></> }
] satisfies Menu[];

const SidebarMenu = () => {
  const router = useRouter();
  const pathname = usePathname();

  React.useEffect(() => {
    if (pathname === ROUTE.BACKLOG) {
      router.replace(ROUTE.HOME);
      return;
    }

    router.replace(pathname);
  }, []);

  return (
    <NavigationMenu className="flex justify-start">
      <NavigationMenuList className="flex flex-col gap-2 text-sm">
        {menu.map((item) => (
          <MenuItem key={item.title} onClick={() => router.push(item.href)} selected={pathname === item.href}>
            <Flex className="gap-2 px-2 py-2">
              {item.icon}
              <span className="text-sm font-semibold">{item.title}</span>
            </Flex>
          </MenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
};

export default SidebarMenu;

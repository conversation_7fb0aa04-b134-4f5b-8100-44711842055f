import '@/styles/globals.css';
import '@repo/ui/styles.css';

import Header from '@/shared/components/layouts/header';
import Sidebar from '@/shared/components/layouts/sidebar';
import DndProvider from '@/shared/providers/DndProvider';

import localFont from 'next/font/local';
import React from 'react';

const poppins = localFont({
  src: './fonts/Poppins.ttf',
  variable: '--font-poppins'
});

const nunitoSans = localFont({
  src: './fonts/NunitoSans.ttf',
  variable: '--font-nunito-sans'
});

export const metadata = { title: 'Project Management', description: '' };

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en">
      <body className={`${poppins.variable} ${nunitoSans.variable} w-screen h-screen bg-white`}>
        <DndProvider>
          <div className="flex w-full h-full">
            <nav className="w-64 min-w-64">
              <Sidebar />
            </nav>

            <div className="flex flex-col w-full h-full">
              <Header />
              <main className="p-10">{children}</main>
            </div>
          </div>
        </DndProvider>
      </body>
    </html>
  );
}
